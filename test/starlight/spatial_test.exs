defmodule Starlight.SpatialTest do
  use Starlight.DataCase

  alias <PERSON>light.{Spatial, Routes, Bridges}
  alias Starlight.Routes.Route
  alias Starlight.Bridges.Bridge

  describe "minimum_distance_to_route/2" do
    test "handles empty coordinate list" do
      point = {40.7128, -74.0060}  # New York City
      empty_coords = []
      
      result = Spatial.minimum_distance_to_route(point, empty_coords)
      assert result == :infinity
    end

    test "handles single coordinate" do
      point = {40.7128, -74.0060}  # New York City
      single_coord = [[-74.0060, 40.7128]]  # Same location as point
      
      result = Spatial.minimum_distance_to_route(point, single_coord)
      assert is_number(result)
      assert result < 0.001  # Should be very close to 0
    end

    test "handles two coordinates (line segment)" do
      point = {40.7128, -74.0060}  # New York City
      line_coords = [[-74.0060, 40.7128], [-73.9352, 40.7306]]  # NYC to Brooklyn
      
      result = Spatial.minimum_distance_to_route(point, line_coords)
      assert is_number(result)
      assert result >= 0
    end

    test "handles multiple coordinates" do
      point = {40.7128, -74.0060}  # New York City
      route_coords = [
        [-74.0060, 40.7128],  # NYC
        [-73.9352, 40.7306],  # Brooklyn
        [-73.7562, 40.7831]   # Queens
      ]
      
      result = Spatial.minimum_distance_to_route(point, route_coords)
      assert is_number(result)
      assert result >= 0
    end
  end

  describe "bridge_intersects_route?/3" do
    setup do
      # Create a test bridge
      bridge_attrs = %{
        name: "Test Bridge",
        latitude: Decimal.new("40.7128"),
        longitude: Decimal.new("-74.0060"),
        span_lengths: [Decimal.new("50.0")],
        support_conditions: ["fixed", "pinned"],
        elastic_modulus: Decimal.new("200.0"),
        moment_of_inertia: [Decimal.new("0.1")]
      }
      
      {:ok, bridge} = Bridges.create_bridge(bridge_attrs)
      {:ok, bridge: bridge}
    end

    test "handles empty route coordinates", %{bridge: bridge} do
      empty_coords = []
      
      result = Spatial.bridge_intersects_route?(bridge, empty_coords, 1.0)
      assert result == false
    end

    test "handles single coordinate route", %{bridge: bridge} do
      # Single coordinate near the bridge
      single_coord = [[-74.0060, 40.7128]]
      
      result = Spatial.bridge_intersects_route?(bridge, single_coord, 1.0)
      assert result == true
    end

    test "handles multi-coordinate route", %{bridge: bridge} do
      # Route that passes near the bridge
      route_coords = [
        [-74.0060, 40.7128],  # Near bridge
        [-73.9352, 40.7306]   # Away from bridge
      ]
      
      result = Spatial.bridge_intersects_route?(bridge, route_coords, 1.0)
      assert result == true
    end

    test "handles route far from bridge", %{bridge: bridge} do
      # Route far from the bridge
      route_coords = [
        [-73.0000, 41.0000],  # Far from bridge
        [-72.0000, 41.0000]   # Also far from bridge
      ]
      
      result = Spatial.bridge_intersects_route?(bridge, route_coords, 1.0)
      assert result == false
    end
  end

  describe "find_bridges_intersecting_route/2" do
    setup do
      # Create test bridges
      bridge1_attrs = %{
        name: "Bridge 1",
        latitude: Decimal.new("40.7128"),
        longitude: Decimal.new("-74.0060"),
        span_lengths: [Decimal.new("50.0")],
        support_conditions: ["fixed", "pinned"],
        elastic_modulus: Decimal.new("200.0"),
        moment_of_inertia: [Decimal.new("0.1")]
      }
      
      bridge2_attrs = %{
        name: "Bridge 2",
        latitude: Decimal.new("41.0000"),
        longitude: Decimal.new("-73.0000"),
        span_lengths: [Decimal.new("30.0")],
        support_conditions: ["pinned", "roller"],
        elastic_modulus: Decimal.new("200.0"),
        moment_of_inertia: [Decimal.new("0.05")]
      }
      
      {:ok, bridge1} = Bridges.create_bridge(bridge1_attrs)
      {:ok, bridge2} = Bridges.create_bridge(bridge2_attrs)
      
      {:ok, bridge1: bridge1, bridge2: bridge2}
    end

    test "handles empty route", %{bridge1: _bridge1, bridge2: _bridge2} do
      route_attrs = %{
        name: "Empty Route",
        coordinates: %{"type" => "LineString", "coordinates" => []}
      }
      
      {:ok, route} = Routes.create_route(route_attrs)
      
      result = Spatial.find_bridges_intersecting_route(route)
      assert result == []
    end

    test "handles single coordinate route", %{bridge1: bridge1, bridge2: _bridge2} do
      route_attrs = %{
        name: "Single Point Route",
        coordinates: %{"type" => "LineString", "coordinates" => [[-74.0060, 40.7128]]}
      }
      
      {:ok, route} = Routes.create_route(route_attrs)
      
      result = Spatial.find_bridges_intersecting_route(route, 1.0)
      bridge_ids = Enum.map(result, & &1.id)
      assert bridge1.id in bridge_ids
    end

    test "handles multi-coordinate route", %{bridge1: bridge1, bridge2: _bridge2} do
      route_attrs = %{
        name: "Multi Point Route",
        coordinates: %{
          "type" => "LineString", 
          "coordinates" => [
            [-74.0060, 40.7128],  # Near bridge1
            [-73.9352, 40.7306]   # Away from both bridges
          ]
        }
      }
      
      {:ok, route} = Routes.create_route(route_attrs)
      
      result = Spatial.find_bridges_intersecting_route(route, 1.0)
      bridge_ids = Enum.map(result, & &1.id)
      assert bridge1.id in bridge_ids
    end
  end

  describe "real_time_intersection_detection/2" do
    setup do
      # Create a test bridge
      bridge_attrs = %{
        name: "Real-time Test Bridge",
        latitude: Decimal.new("40.7128"),
        longitude: Decimal.new("-74.0060"),
        span_lengths: [Decimal.new("50.0")],
        support_conditions: ["fixed", "pinned"],
        elastic_modulus: Decimal.new("200.0"),
        moment_of_inertia: [Decimal.new("0.1")]
      }
      
      {:ok, bridge} = Bridges.create_bridge(bridge_attrs)
      {:ok, bridge: bridge}
    end

    test "handles empty coordinates", %{bridge: _bridge} do
      result = Spatial.real_time_intersection_detection([])
      assert result == []
    end

    test "handles single coordinate", %{bridge: bridge} do
      coords = [[-74.0060, 40.7128]]  # Near bridge
      
      result = Spatial.real_time_intersection_detection(coords, 1.0)
      assert bridge.id in result
    end

    test "handles multiple coordinates", %{bridge: bridge} do
      coords = [
        [-74.0060, 40.7128],  # Near bridge
        [-73.9352, 40.7306]   # Away from bridge
      ]
      
      result = Spatial.real_time_intersection_detection(coords, 1.0)
      assert bridge.id in result
    end
  end
end
