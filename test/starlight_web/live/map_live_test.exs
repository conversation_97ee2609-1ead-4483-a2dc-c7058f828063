defmodule StarlightWeb.MapLiveTest do
  use StarlightWeb.ConnCase

  import Phoenix.LiveViewTest
  import Starlight.AccountsFixtures

  describe "MapLive" do
    setup do
      %{user: user_fixture()}
    end

    test "shows add bridge button when authenticated", %{conn: conn, user: user} do
      {:ok, _index_live, html} = 
        conn
        |> log_in_user(user)
        |> live(~p"/map")

      assert html =~ "Add Bridge"
    end

    test "can open bridge form modal", %{conn: conn, user: user} do
      {:ok, index_live, _html} = 
        conn
        |> log_in_user(user)
        |> live(~p"/map")

      # Click the Add Bridge button
      index_live |> element("button", "Add Bridge") |> render_click()

      # Check that the bridge form modal is displayed
      assert has_element?(index_live, "[data-testid='bridge-form-modal']") ||
             has_element?(index_live, ".live_component") ||
             render(index_live) =~ "Add New Bridge"
    end

    test "bridge form has current_user available", %{conn: conn, user: user} do
      {:ok, index_live, _html} = 
        conn
        |> log_in_user(user)
        |> live(~p"/map")

      # Click the Add Bridge button to open the form
      index_live |> element("button", "Add Bridge") |> render_click()

      # The form should be displayed without crashing
      # If current_user was nil, this would cause a crash when trying to save
      assert render(index_live) =~ "Bridge Name"
      assert render(index_live) =~ "Add New Bridge"
    end
  end
end
