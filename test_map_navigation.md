# Map Navigation Testing Guide

## Testing the Vehicle Selection Map Disappearing Issue Fix

This guide helps you test the fixes implemented for the specific issue where selecting a vehicle from the dropdown caused the map to disappear.

## Pre-Testing Setup

1. **Start the Phoenix server:**
   ```bash
   mix phx.server
   ```

2. **Open browser console** (F12) to monitor debug messages

3. **Navigate to the map page** (usually `http://localhost:4000`)

## Test Cases

### Test 1: Basic Map Loading
- [ ] Map loads successfully on page load
- [ ] Bridge markers appear on the map
- [ ] Console shows "Map initialized successfully"
- [ ] No JavaScript errors in console

### Test 2: Vehicle Selection (Primary Issue)
- [ ] Click "Select Vehicle" button
- [ ] Vehicle selection modal opens
- [ ] **Map remains visible behind the modal**
- [ ] Select any vehicle from the list
- [ ] Modal closes and flash message appears
- [ ] **Map is still visible and functional**
- [ ] Vehicle name appears in header
- [ ] Console shows "Vehicle selected: [vehicle-name]"
- [ ] **No "MapHook destroyed" messages in console**

### Test 3: Bridge Navigation
- [ ] Click on a bridge marker
- [ ] Bridge details appear in sidebar
- [ ] Bridge gets highlighted (larger icon with animation)
- [ ] Map pans to bridge location
- [ ] Map remains visible throughout the process

### Test 4: Multiple Vehicle Selections
- [ ] Select a vehicle, verify map stays visible
- [ ] Click "Change" to select a different vehicle
- [ ] **Map remains visible during second selection**
- [ ] Select different vehicle
- [ ] **Map is still visible and functional**
- [ ] No console errors or hook destruction

### Test 5: URL Navigation to Bridge
- [ ] Navigate directly to a bridge URL (e.g., `/bridges/[bridge-id]`)
- [ ] Map loads and remains visible
- [ ] Bridge is automatically highlighted
- [ ] Console shows "Highlighting bridge: [bridge-name]"

### Test 6: Clear Selection
- [ ] Click "Clear Selection" button
- [ ] Bridge highlighting is removed
- [ ] Map remains visible
- [ ] Console shows "Clearing bridge highlights"

### Test 7: Route Drawing
- [ ] Use the drawing tool to draw a route
- [ ] Map remains visible during drawing
- [ ] Intersecting bridges are highlighted
- [ ] No performance issues or map disappearing

## Debug Commands

Use these commands in the browser console for debugging:

```javascript
// Check map state
debugMap()

// Reinitialize map if needed
reinitializeMap()

// Enable LiveView debugging
liveSocket.enableDebug()
```

## Expected Console Messages

When working correctly, you should see:
- "MapHook mounted"
- "Map initialized successfully"
- "Received load_bridges event with X bridges"
- "Successfully added X bridge markers"
- "Vehicle selected: [vehicle-name]" (in server logs)
- **NO "MapHook destroyed" messages during vehicle selection**
- "Highlighting bridge: [bridge-name]"

## Troubleshooting

If the map still disappears:

1. **Check console for errors**
2. **Run `debugMap()` to see current state**
3. **Try `reinitializeMap()` to recover**
4. **Check if container is visible** (should show in debug output)

## Performance Monitoring

- Bridge loading should be fast (< 1 second)
- Map navigation should be smooth
- No memory leaks (check browser dev tools)
- Real-time intersection detection should be throttled

## Success Criteria

✅ Map never disappears during navigation
✅ Bridge highlighting works consistently  
✅ No JavaScript errors in console
✅ Smooth performance during all operations
✅ Map recovers gracefully from any errors
